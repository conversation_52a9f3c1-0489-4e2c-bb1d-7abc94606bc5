{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qvoEkEXDtojn6YyT8ZQA/ow2v3+7NzWJnIPfnYYBNAs=", "__NEXT_PREVIEW_MODE_ID": "65989ca6e84ce603be9c16ed01c1fa72", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ad7b03748b54f0deddb0bf2ecd34e6c57d9bb320c74fe279a2eb0b462f2dc2db", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e4bcd1ded5b2ef0e1dd60fb204e5e88aca76caccc3f6769d605d42f6fe1425e2"}}}, "instrumentation": null, "functions": {}}