{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qvoEkEXDtojn6YyT8ZQA/ow2v3+7NzWJnIPfnYYBNAs=", "__NEXT_PREVIEW_MODE_ID": "8b4cca29875c712cfe8040a42ca720f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a2bf90e21b6b1ae1451e22b0d620d194f100ca87bf3092dcd700222f0bfa8588", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eba221a98a0d8d3be834088f7174bf5e0d5dc22c16d3fa68b7f20a332684dec4"}}}, "instrumentation": null, "functions": {}}