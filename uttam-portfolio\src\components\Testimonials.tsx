'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Quote, Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getFeaturedTestimonials, type Testimonial } from '@/lib/api';

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const featuredTestimonials = await getFeaturedTestimonials();
        setTestimonials(featuredTestimonials);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  useEffect(() => {
    if (!isAutoPlaying || testimonials.length === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume auto-play after 10 seconds
  };

  const goToPrevious = () => {
    const newIndex = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;
    goToSlide(newIndex);
  };

  const goToNext = () => {
    const newIndex = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;
    goToSlide(newIndex);
  };

  const currentTestimonial = testimonials[currentIndex];

  if (loading) {
    return (
      <section id="testimonials" className="py-20 bg-gradient-to-br from-portfolio-primary via-portfolio-dark to-portfolio-primary">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-4">
              What Clients Say
            </h2>
            <p className="text-lg text-white/80 max-w-2xl mx-auto">
              Feedback from collaborators and clients who trusted me with their vision.
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
              <CardContent className="p-8 md:p-12 text-center">
                <div className="space-y-4">
                  <div className="w-20 h-20 bg-white/20 rounded-full mx-auto animate-pulse"></div>
                  <div className="h-6 bg-white/20 rounded animate-pulse mx-auto w-3/4"></div>
                  <div className="h-4 bg-white/20 rounded animate-pulse mx-auto w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    );
  }

  if (testimonials.length === 0) {
    return (
      <section id="testimonials" className="py-20 bg-gradient-to-br from-portfolio-primary via-portfolio-dark to-portfolio-primary">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-4">
              What Clients Say
            </h2>
            <p className="text-lg text-white/80 max-w-2xl mx-auto">
              Feedback from collaborators and clients who trusted me with their vision.
            </p>
          </div>
          <div className="text-center py-12">
            <p className="text-lg text-white/80">No testimonials available at the moment.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="testimonials" className="py-20 bg-gradient-to-br from-portfolio-primary via-portfolio-dark to-portfolio-primary">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-white mb-4">
            What Clients Say
          </h2>
          <p className="text-lg text-white/80 max-w-2xl mx-auto">
            Feedback from collaborators and clients who trusted me with their vision.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Navigation Arrows */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute -left-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-portfolio-accent hover:bg-white/10"
              onClick={goToPrevious}
            >
              <ChevronLeft size={24} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute -right-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-portfolio-accent hover:bg-white/10"
              onClick={goToNext}
            >
              <ChevronRight size={24} />
            </Button>

            {/* Testimonial Card */}
            <Card className="bg-white/10 backdrop-blur-md border-white/20 text-white">
              <CardContent className="p-8 md:p-12 text-center">
                {/* Quote Icon */}
                <div className="flex justify-center mb-6">
                  <Quote size={48} className="text-portfolio-accent opacity-50" />
                </div>

                {/* Avatar */}
                <div className="flex justify-center mb-6">
                  <div className="relative w-20 h-20 rounded-full overflow-hidden border-4 border-portfolio-accent/50">
                    {currentTestimonial.avatar ? (
                      <Image
                        src={currentTestimonial.avatar}
                        alt={currentTestimonial.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-portfolio-accent flex items-center justify-center text-white font-heading font-bold text-2xl">
                        {currentTestimonial.name.charAt(0)}
                      </div>
                    )}
                  </div>
                </div>

                {/* Rating */}
                {currentTestimonial.rating && (
                  <div className="flex justify-center gap-1 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        size={20}
                        className={`${
                          i < currentTestimonial.rating!
                            ? 'text-yellow-400 fill-current'
                            : 'text-white/30'
                        }`}
                      />
                    ))}
                  </div>
                )}

                {/* Testimonial Content */}
                <blockquote className="text-lg md:text-xl leading-relaxed mb-8 italic">
                  "{currentTestimonial.content}"
                </blockquote>

                {/* Author Info */}
                <div>
                  <cite className="text-portfolio-accent font-semibold text-lg not-italic">
                    {currentTestimonial.name}
                  </cite>
                  <div className="text-white/80 text-sm mt-1">
                    {currentTestimonial.role}
                    {currentTestimonial.company && (
                      <span> at {currentTestimonial.company}</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center gap-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-portfolio-accent scale-125'
                    : 'bg-white/30 hover:bg-white/50'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-white/20">
            {[
              { number: '98%', label: 'Client Satisfaction' },
              { number: '50+', label: 'Projects Delivered' },
              { number: '25+', label: 'Happy Clients' },
              { number: '1M+', label: 'Views Generated' },
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-heading font-bold text-portfolio-accent mb-2">
                  {stat.number}
                </div>
                <div className="text-white/80 font-medium text-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
