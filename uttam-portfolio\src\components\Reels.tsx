'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getFeaturedReels, type Reel, getYouTubeShortsThumbnail } from '@/lib/api';

export default function Reels() {
  const [selectedReel, setSelectedReel] = useState<string | null>(null);
  const [reels, setReels] = useState<Reel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchReels = async () => {
      try {
        const featuredReels = await getFeaturedReels();
        console.log(featuredReels);
        // Show only first 12 shorts for featured section
        const shortsWithThumbnails = featuredReels.slice(0, 12).map(reel => ({
          ...reel,
          thumbnail: reel.thumbnail || getYouTubeShortsThumbnail(reel.id)
        }));
        setReels(shortsWithThumbnails);
      } catch (error) {
        console.error('Error fetching featured shorts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReels();
  }, []);

  const openReelModal = (reelId: string) => {
    setSelectedReel(reelId);
  };

  const navigateReel = (direction: 'prev' | 'next') => {
    if (!selectedReel) return;

    const currentIndex = reels.findIndex(reel => reel.id === selectedReel);
    let newIndex;

    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : reels.length - 1;
    } else {
      newIndex = currentIndex < reels.length - 1 ? currentIndex + 1 : 0;
    }

    setSelectedReel(reels[newIndex].id);
  };

  const selectedReelData = reels.find(reel => reel.id === selectedReel);

  // if(!loading && reels.length !== 0) {
  //   return console.log(reels);
  // }

  return (
    <section id="shorts" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-foreground mb-4">
            YouTube Shorts
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto font-body">
            Creative short-form content showcasing dynamic editing and storytelling.
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {[...Array(12)].map((_, index) => (
              <div key={index} className="bg-card rounded-xl overflow-hidden shadow-lg">
                <div className="aspect-[9/16] bg-gray-200 animate-pulse"></div>
                <div className="p-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : reels.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground font-body">No YouTube Shorts available at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {reels.map((reel) => (
              <div
                key={reel._id}
                className="group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105"
              >
                <div className="relative aspect-[9/16] overflow-hidden">
                  <Image
                    src={reel.thumbnail}
                    alt={reel.title}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />

                  {/* Play Button Overlay */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <button
                        className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        onClick={() => openReelModal(reel.id)}
                      >
                        <div className="w-12 h-12 bg-accent hover:bg-accent/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg">
                          <Play size={16} className="text-accent-foreground ml-0.5" fill="currentColor" />
                        </div>
                      </button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md w-full p-0 bg-black border-0">
                      <div className="relative">
                        {/* Navigation Arrows */}
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute -left-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent"
                          onClick={() => navigateReel('prev')}
                        >
                          <ChevronLeft size={20} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute -right-16 top-1/2 -translate-y-1/2 z-10 text-white hover:text-accent"
                          onClick={() => navigateReel('next')}
                        >
                          <ChevronRight size={20} />
                        </Button>

                        <button
                          onClick={() => setSelectedReel(null)}
                          className="absolute -top-12 right-0 z-50 text-white hover:text-accent transition-colors"
                        >
                          <X size={24} />
                        </button>

                        <div className="aspect-[9/16] max-h-[80vh]">
                          {selectedReel === reel.id && selectedReelData?.embedUrl && (
                            <iframe
                              src={selectedReelData.embedUrl}
                              title={selectedReelData.title}
                              className="w-full h-full"
                              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                              allowFullScreen
                            />
                          )}
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Platform Badge */}
                  <div className="absolute top-2 left-2">
                    <Badge variant="secondary" className="bg-accent/90 text-accent-foreground text-xs font-accent">
                      YouTube Shorts
                    </Badge>
                  </div>
                </div>

                <div className="p-3">
                  <h3 className="text-sm font-heading font-semibold text-foreground mb-1 group-hover:text-accent transition-colors duration-300 line-clamp-2">
                    {reel.title}
                  </h3>
                  {reel.description && (
                    <p className="text-muted-foreground text-xs leading-relaxed line-clamp-2 font-body">
                      {reel.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* View More Button */}
        <div className="text-center mt-12">
          <Link href="/reels">
            <button className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-full font-semibold font-accent transition-all duration-300 hover:scale-105 hover:shadow-lg">
              View All YouTube Shorts
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
