'use client';

import { useState, useEffect } from 'react';
import { MessageCircle } from 'lucide-react';

export default function WhatsAppButton() {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  // Your WhatsApp number (replace with actual number)
  const whatsappNumber = '+977XXXXXXXXXX'; // Replace with your actual WhatsApp number
  const message = encodeURIComponent('Hi! I found your portfolio and would like to discuss a video editing project.');

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  useEffect(() => {
    // Show tooltip after 3 seconds of being visible
    if (isVisible) {
      const timer = setTimeout(() => {
        setShowTooltip(true);
        // Hide tooltip after 5 seconds
        setTimeout(() => setShowTooltip(false), 5000);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  const handleWhatsAppClick = () => {
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank', 'noopener,noreferrer');
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-24 right-8 z-50">
      {/* Auto-show Tooltip */}
      <div className={`
        absolute right-16 top-1/2 -translate-y-1/2 bg-green-600 text-white px-4 py-3 rounded-lg text-sm whitespace-nowrap
        transition-all duration-500 pointer-events-none shadow-lg
        ${showTooltip && !isHovered ? 'opacity-100 translate-x-0 scale-100' : 'opacity-0 translate-x-4 scale-95'}
      `}>
        💬 Need help with video editing?
        <div className="absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-green-600 rotate-45"></div>
      </div>

      <button
        onClick={handleWhatsAppClick}
        onMouseEnter={() => {
          setIsHovered(true);
          setShowTooltip(false);
        }}
        onMouseLeave={() => setIsHovered(false)}
        className={`group transition-all duration-300 ${
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'
        }`}
        aria-label="Contact via WhatsApp"
      >
        {/* Main WhatsApp Button */}
        <div className="relative">
          {/* Ripple Effect Background */}
          <div className="absolute inset-0 rounded-full bg-green-400 opacity-30 animate-ping"></div>
          <div className="absolute inset-0 rounded-full bg-green-400 opacity-20 animate-ping animation-delay-1000"></div>

          <div className={`
            relative bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl
            transition-all duration-300 transform hover:scale-110 group-hover:rotate-12
            ${isHovered ? 'shadow-green-500/50' : ''}
          `}>
            <MessageCircle size={24} className="transition-transform duration-300" />
          </div>

          {/* Notification Dot */}
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
        </div>

        {/* Hover Tooltip */}
        <div className={`
          absolute right-16 top-1/2 -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap
          transition-all duration-300 pointer-events-none shadow-lg
          ${isHovered ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}
        `}>
          Chat with us on WhatsApp
          <div className="absolute top-1/2 -translate-y-1/2 -right-1 w-2 h-2 bg-gray-900 rotate-45"></div>
        </div>
      </button>
    </div>
  );
}
