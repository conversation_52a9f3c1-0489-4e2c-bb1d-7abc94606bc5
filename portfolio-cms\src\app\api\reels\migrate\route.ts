import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Short from "@/models/Short";
import { requireAuth } from "@/lib/auth";

// POST /api/reels/migrate - Migrate existing shorts to YouTube Shorts with auto-generated thumbnails
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    // Find all shorts that need migration
    const shorts = await Short.find({});

    let migratedCount = 0;
    let errors: string[] = [];

    for (const short of shorts) {
      try {
        const updates: any = {};

        // Set platform to YouTube if not already
        if (short.platform !== "youtube") {
          updates.platform = "youtube";
        }

        // Generate thumbnail if missing and we have an ID
        if (!short.thumbnail && short.id) {
          updates.thumbnail = `https://img.youtube.com/vi/${short.id}/maxresdefault.jpg`;
        }

        // Update embed URL to YouTube format if needed
        if (
          short.id &&
          (!short.embedUrl || !short.embedUrl.includes("youtube.com"))
        ) {
          updates.embedUrl = `https://www.youtube.com/embed/${short.id}`;
        }

        // Only update if there are changes
        if (Object.keys(updates).length > 0) {
          await Short.findByIdAndUpdate(short._id, updates);
          migratedCount++;
        }
      } catch (error) {
        console.error(`Error migrating short ${short._id}:`, error);
        errors.push(`Failed to migrate short "${short.title}": ${error}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Migration completed. ${migratedCount} shorts updated.`,
      migratedCount,
      totalShorts: shorts.length,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Migration error:", error);
    return NextResponse.json({ error: "Migration failed" }, { status: 500 });
  }
});
