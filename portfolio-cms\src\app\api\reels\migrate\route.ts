import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Short from "@/models/Short";
import { requireAuth } from "@/lib/auth";

// POST /api/reels/migrate - Migrate existing shorts to YouTube Shorts with auto-generated thumbnails
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    // Find all reels that need migration
    const reels = await Reel.find({});

    let migratedCount = 0;
    let errors: string[] = [];

    for (const reel of reels) {
      try {
        const updates: any = {};

        // Set platform to YouTube if not already
        if (reel.platform !== "youtube") {
          updates.platform = "youtube";
        }

        // Generate thumbnail if missing and we have an ID
        if (!reel.thumbnail && reel.id) {
          updates.thumbnail = `https://img.youtube.com/vi/${reel.id}/maxresdefault.jpg`;
        }

        // Update embed URL to YouTube format if needed
        if (
          reel.id &&
          (!reel.embedUrl || !reel.embedUrl.includes("youtube.com"))
        ) {
          updates.embedUrl = `https://www.youtube.com/embed/${reel.id}`;
        }

        // Only update if there are changes
        if (Object.keys(updates).length > 0) {
          await Reel.findByIdAndUpdate(reel._id, updates);
          migratedCount++;
        }
      } catch (error) {
        console.error(`Error migrating reel ${reel._id}:`, error);
        errors.push(`Failed to migrate reel "${reel.title}": ${error}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Migration completed. ${migratedCount} reels updated.`,
      migratedCount,
      totalReels: reels.length,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Migration error:", error);
    return NextResponse.json({ error: "Migration failed" }, { status: 500 });
  }
});
