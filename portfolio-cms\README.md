# Portfolio CMS

A modern, full-stack Content Management System built with Next.js, MongoDB, and JWT authentication for managing portfolio content.

## 🚀 Features

### ✅ **Authentication & Security**
- JWT-based authentication
- Secure password hashing with bcrypt
- Role-based access control (Admin/Editor)
- HTTP-only cookies for token storage

### ✅ **Content Management**
- **Blog Posts**: Rich text editor with SEO optimization
- **Projects**: Portfolio project management
- **Testimonials**: Client testimonial management
- **Clients**: Client information and showcase

### ✅ **Rich Text Editor**
- TipTap-based WYSIWYG editor
- Image insertion and management
- Link management
- Text formatting and styling
- Code blocks and lists

### ✅ **SEO Optimization**
- SEO-friendly slugs (auto-generated)
- Meta titles and descriptions
- Keywords management
- Open Graph support

### ✅ **Modern UI/UX**
- Built with shadcn/ui components
- Responsive design
- Dark mode support
- Intuitive dashboard interface

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT (jsonwebtoken)
- **UI**: Tailwind CSS, shadcn/ui
- **Rich Text**: TipTap Editor
- **Icons**: Lucide React

## 📋 Prerequisites

Before running the CMS, make sure you have:

1. **Node.js** (v18 or higher)
2. **MongoDB** running locally or MongoDB Atlas account
3. **npm** or **yarn** package manager

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Setup

Create a `.env.local` file in the root directory:

```env
# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/portfolio-cms

# JWT Secret (change in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Next Auth Secret
NEXTAUTH_SECRET=your-nextauth-secret-key

# App URL
NEXTAUTH_URL=http://localhost:3002

# Admin Credentials (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Portfolio Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
```

### 3. Database Setup

**Option A: Local MongoDB**
1. Download and install MongoDB Community Edition
2. Start MongoDB service
3. Use default connection: `mongodb://localhost:27017`

**Option B: MongoDB Atlas (Cloud)**
1. Create free account at [MongoDB Atlas](https://cloud.mongodb.com)
2. Create a cluster
3. Get connection string and update `MONGODB_URI`

### 4. Start Development Server

```bash
npm run dev
```

The CMS will be available at: `http://localhost:3002`

### 5. Initial Setup

1. Visit `http://localhost:3002`
2. The system will automatically redirect to `/setup`
3. Click "Start Setup" to initialize the database and admin user
4. Once setup is complete, login with the admin credentials

## 🔐 Default Admin Credentials

```
Email: <EMAIL>
Password: admin123
```

**⚠️ Important**: Change these credentials in production!
