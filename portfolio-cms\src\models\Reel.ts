import mongoose, { Document, Schema } from 'mongoose';

export interface IReel extends Document {
  id: string;
  title: string;
  slug: string;
  description?: string;
  platform: 'instagram' | 'youtube';
  embedUrl?: string;
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const ReelSchema = new Schema<IReel>({
  id: {
    type: String,
    required: true,
    trim: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  description: {
    type: String,
    maxlength: 500,
  },
  platform: {
    type: String,
    enum: ['instagram', 'youtube'],
    required: true,
  },
  embedUrl: {
    type: String,
  },
  featured: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  },
  order: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Indexes for better performance
ReelSchema.index({ slug: 1 });
ReelSchema.index({ status: 1, order: 1 });
ReelSchema.index({ platform: 1 });
ReelSchema.index({ featured: 1 });

// Pre-save middleware to auto-generate slug and embedUrl
ReelSchema.pre('save', function(next) {
  if (!this.slug && this.title) {
    const slugify = require('slugify');
    this.slug = slugify(this.title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });
  }

  // Auto-generate embed URL based on platform and id
  if (this.id && !this.embedUrl) {
    switch (this.platform) {
      case 'instagram':
        this.embedUrl = `https://www.instagram.com/reel/${this.id}/embed`;
        break;
      case 'youtube':
        this.embedUrl = `https://www.youtube.com/embed/${this.id}`;
        break;
    }
  }

  next();
});

export default mongoose.models.Reel || mongoose.model<IReel>('Reel', ReelSchema);
