{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport default function Home() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [systemStatus, setSystemStatus] = useState<'checking' | 'ready' | 'setup-needed'>('checking');\n\n  useEffect(() => {\n    checkSystemStatus();\n  }, []);\n\n  const checkSystemStatus = async () => {\n    try {\n      const response = await fetch('/api/status');\n      const data = await response.json();\n\n      if (data.success && data.status.database === 'connected' && data.status.adminUser === 'exists') {\n        setSystemStatus('ready');\n      } else {\n        setSystemStatus('setup-needed');\n      }\n    } catch (error) {\n      setSystemStatus('setup-needed');\n    }\n  };\n\n  useEffect(() => {\n    if (!loading && systemStatus !== 'checking') {\n      if (systemStatus === 'setup-needed') {\n        router.push('/setup');\n      } else if (user) {\n        router.push('/dashboard');\n      } else {\n        router.push('/login');\n      }\n    }\n  }, [user, loading, systemStatus, router]);\n\n  if (loading || systemStatus === 'checking') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">\n            {systemStatus === 'checking' ? 'Checking system status...' : 'Loading...'}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyC;IAExF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,CAAC,QAAQ,KAAK,eAAe,KAAK,MAAM,CAAC,SAAS,KAAK,UAAU;gBAC9F,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW,iBAAiB,YAAY;gBAC3C,IAAI,iBAAiB,gBAAgB;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,MAAM;oBACf,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAM;QAAS;QAAc;KAAO;IAExC,IAAI,WAAW,iBAAiB,YAAY;QAC1C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCACV,iBAAiB,aAAa,8BAA8B;;;;;;;;;;;;;;;;;IAKvE;IAEA,OAAO;AACT;GAlDwB;;QACI,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}