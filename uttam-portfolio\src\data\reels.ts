export interface Reel {
  id: string;
  title: string;
  thumbnail: string;
  description?: string;
  platform: 'instagram' | 'tiktok' | 'youtube';
  embedUrl?: string;
}

export const reels: Reel[] = [
  {
    id: 'Cq1qzQ8gQ7f',
    title: '  ',
    thumbnail: '/images/placeholder.svg',
    description: 'Smooth and dynamic transitions for social media content.',
    platform: 'instagram',
    embedUrl: 'https://www.instagram.com/reel/Cq1qzQ8gQ7f/embed'
  },
  {
    id: 'Cq_QZQ9gQ9g',
    title: 'Fast-Paced Promo',
    thumbnail: '/images/placeholder.svg',
    description: 'High-energy promotional content with quick cuts and engaging visuals.',
    platform: 'instagram',
    embedUrl: 'https://www.instagram.com/reel/Cq_QZQ9gQ9g/embed'
  },
  {
    id: 'CrEdRaUgRrL',
    title: 'Cinematic Color Grade',
    thumbnail: '/images/placeholder.svg',
    description: 'Professional color grading techniques for cinematic look.',
    platform: 'instagram',
    embedUrl: 'https://www.instagram.com/reel/CrEdRaUgRrL/embed'
  },
  {
    id: 'CsF2dQ7gS8h',
    title: 'Motion Graphics Demo',
    thumbnail: '/images/placeholder.svg',
    description: 'Creative motion graphics and text animations.',
    platform: 'instagram',
    embedUrl: 'https://www.instagram.com/reel/CsF2dQ7gS8h/embed'
  },
  {
    id: 'CtG3eR8hT9i',
    title: 'Behind the Scenes',
    thumbnail: '/images/placeholder.svg',
    description: 'Behind the scenes of video editing process.',
    platform: 'instagram',
    embedUrl: 'https://www.instagram.com/reel/CtG3eR8hT9i/embed'
  },
  {
    id: 'CuH4fS9iU0j',
    title: 'Quick Tips',
    thumbnail: '/images/placeholder.svg',
    description: 'Quick editing tips and tricks for content creators.',
    platform: 'instagram',
    embedUrl: 'https://www.instagram.com/reel/CuH4fS9iU0j/embed'
  }
];
