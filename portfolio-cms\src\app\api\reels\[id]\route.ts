import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Reel from "@/models/Reel";
import { requireAuth } from "@/lib/auth";
import slugify from "slugify";

// GET /api/reels/[id] - Get single reel
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    const { id } = await params;

    // Find by ID or slug
    const reel = await Reel.findOne({
      $or: [{ _id: id }, { slug: id }],
    }).lean();

    if (!reel) {
      return NextResponse.json({ error: "Reel not found" }, { status: 404 });
    }

    // Add auto-generated thumbnail if missing
    const reelWithThumbnail = {
      ...reel,
      thumbnail:
        reel.thumbnail ||
        (reel.platform === "youtube" && reel.id
          ? `https://img.youtube.com/vi/${reel.id}/maxresdefault.jpg`
          : "/images/placeholder.svg"),
    };

    return NextResponse.json({
      success: true,
      data: reelWithThumbnail,
    });
  } catch (error) {
    console.error("Get reel error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/reels/[id] - Update reel
export const PUT = requireAuth(
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      await connectDB();

      const { id } = await params;
      const data = await request.json();

      const reel = await Reel.findById(id);
      if (!reel) {
        return NextResponse.json({ error: "Reel not found" }, { status: 404 });
      }

      // Handle slug update if title changed
      if (data.title && data.title !== reel.title) {
        const baseSlug = slugify(data.title, {
          lower: true,
          strict: true,
          remove: /[*+~.()'"!:@]/g,
        });

        // Ensure unique slug (excluding current reel)
        let slug = baseSlug;
        let counter = 1;
        while (await Reel.findOne({ slug, _id: { $ne: id } })) {
          slug = `${baseSlug}-${counter}`;
          counter++;
        }
        data.slug = slug;
      }

      // Update embed URL and thumbnail for YouTube if id changed
      if (data.id && data.id !== reel.id) {
        const reelId = data.id;
        data.embedUrl = `https://www.youtube.com/embed/${reelId}`;
        data.thumbnail = `https://img.youtube.com/vi/${reelId}/maxresdefault.jpg`;
      }

      // Ensure platform is always YouTube
      if (data.platform) {
        data.platform = "youtube";
      }

      // Update reel
      const updatedReel = await Reel.findByIdAndUpdate(
        id,
        { ...data, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      return NextResponse.json({
        success: true,
        data: updatedReel,
      });
    } catch (error) {
      console.error("Update reel error:", error);
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
  }
);

// DELETE /api/reels/[id] - Delete reel
export const DELETE = requireAuth(
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      await connectDB();

      const { id } = await params;

      const reel = await Reel.findById(id);
      if (!reel) {
        return NextResponse.json({ error: "Reel not found" }, { status: 404 });
      }

      await Reel.findByIdAndDelete(id);

      return NextResponse.json({
        success: true,
        message: "Reel deleted successfully",
      });
    } catch (error) {
      console.error("Delete reel error:", error);
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
  }
);
