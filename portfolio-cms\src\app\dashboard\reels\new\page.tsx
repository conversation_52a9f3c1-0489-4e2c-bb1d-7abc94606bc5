'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, Eye, Play, Instagram, Youtube } from 'lucide-react';
import Link from 'next/link';

export default function NewReelPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    id: '',
    title: '',
    description: '',
    platform: 'instagram' as 'instagram' | 'youtube',
    embedUrl: '',
    featured: false,
    status: 'draft' as 'draft' | 'published' | 'archived',
    order: 0,
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateEmbedUrl = (reelId: string) => {
    return `https://www.youtube.com/embed/${reelId}`;
  };

  const generateThumbnail = (reelId: string) => {
    return `https://img.youtube.com/vi/${reelId}/maxresdefault.jpg`;
  };

  const handleSubmit = async (status: 'draft' | 'published') => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const embedUrl = formData.embedUrl || generateEmbedUrl(formData.id);
      const thumbnail = generateThumbnail(formData.id);

      const response = await fetch('/api/reels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...formData,
          embedUrl,
          thumbnail,
          platform: 'youtube',
          status,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Reel ${status === 'published' ? 'published' : 'saved as draft'} successfully!`);
        setTimeout(() => {
          router.push('/dashboard/reels');
        }, 1500);
      } else {
        setError(data.error || 'Failed to save reel');
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return <Instagram className="h-4 w-4" />;
      case 'youtube':
        return <Youtube className="h-4 w-4" />;
      default:
        return <Play className="h-4 w-4" />;
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/reels">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Reels
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">New Reel</h1>
              <p className="text-gray-600">Add a new reel to your portfolio</p>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Reel Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter reel title..."
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Brief description of the reel..."
                    rows={3}
                  />
                </div>



                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="platform">Platform *</Label>
                    <select
                      id="platform"
                      value="youtube"
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-100"
                    >
                      <option value="youtube">YouTube Shorts</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Only YouTube Shorts are supported
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="id">YouTube Video ID *</Label>
                    <Input
                      id="id"
                      value={formData.id}
                      onChange={(e) => handleInputChange('id', e.target.value)}
                      placeholder="e.g., dQw4w9WgXcQ"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      YouTube video ID from the Shorts URL
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="embedUrl">Custom Embed URL (Optional)</Label>
                  <Input
                    id="embedUrl"
                    value={formData.embedUrl}
                    onChange={(e) => handleInputChange('embedUrl', e.target.value)}
                    placeholder="Auto-generated if left empty"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Will be auto-generated based on platform and reel ID if not provided
                  </p>
                </div>

              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Publish</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => handleInputChange('featured', checked)}
                  />
                  <Label htmlFor="featured">Featured Reel</Label>
                </div>

                <div className="space-y-2">
                  <Button
                    onClick={() => handleSubmit('draft')}
                    variant="outline"
                    className="w-full"
                    disabled={loading}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Draft
                  </Button>

                  <Button
                    onClick={() => handleSubmit('published')}
                    className="w-full"
                    disabled={loading || !formData.title || !formData.id}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Publish
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="order">Display Order</Label>
                  <Input
                    id="order"
                    type="number"
                    value={formData.order}
                    onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Lower numbers appear first
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="aspect-[9/16] bg-gray-200 rounded-lg mb-3 overflow-hidden">
                    {formData.id ? (
                      <img
                        src={generateThumbnail(formData.id)}
                        alt={formData.title || 'YouTube Shorts thumbnail'}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/images/placeholder.svg';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Play className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-sm">
                        {formData.title || 'Reel Title'}
                      </h3>
                      <Badge variant="outline" className="text-xs">
                        {getPlatformIcon(formData.platform)}
                        <span className="ml-1">{formData.platform}</span>
                      </Badge>
                    </div>

                    <p className="text-xs text-gray-600">
                      {formData.description || 'Reel description will appear here...'}
                    </p>

                    {formData.featured && (
                      <Badge className="text-xs bg-yellow-100 text-yellow-800">
                        Featured
                      </Badge>
                    )}

                    {formData.id && (
                      <p className="text-xs text-gray-400">
                        ID: {formData.id}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
