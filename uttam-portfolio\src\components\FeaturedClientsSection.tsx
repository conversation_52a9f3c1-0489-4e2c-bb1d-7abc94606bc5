'use client';

import { useState } from 'react';
import { Star } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import ClientCard from '@/components/ClientCard';
import { type Client } from '@/lib/api';

interface FeaturedClientsSectionProps {
  featuredClients: Client[];
  allClients: Client[];
}

export default function FeaturedClientsSection({
  featuredClients,
  allClients
}: FeaturedClientsSectionProps) {
  const [selectedClient, setSelectedClient] = useState<string | null>(null);

  const openClientModal = (clientId: string) => {
    setSelectedClient(clientId);
  };

  const selectedClientData = allClients.find(client => client._id === selectedClient);

  if (featuredClients.length === 0) {
    return null;
  }

  return (
    <section className="mb-16">
      <div className="flex items-center gap-3 mb-8">
        <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl">
          <Star size={24} className="text-white" />
        </div>
        <div>
          <h2 className="text-3xl font-heading font-bold text-portfolio-primary">
            Featured Partnerships
          </h2>
          <p className="text-muted-foreground mt-1">
            Our most valued collaborations and success stories
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {featuredClients.map((client) => (
          <Dialog key={client._id}>
            <DialogTrigger asChild>
              <div className="group relative">
                <ClientCard
                  client={client}
                  variant="compact"
                  onClick={() => openClientModal(client._id)}
                  showWebsite={false}
                  className="hover:shadow-xl transition-all duration-500 hover:-translate-y-2"
                />
                {/* Featured Badge */}
                <div className="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Star size={12} />
                  Featured
                </div>
              </div>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              {selectedClient === client._id && selectedClientData && (
                <ClientCard
                  client={selectedClientData}
                  variant="detailed"
                  showWebsite={true}
                />
              )}
            </DialogContent>
          </Dialog>
        ))}
      </div>
    </section>
  );
}
