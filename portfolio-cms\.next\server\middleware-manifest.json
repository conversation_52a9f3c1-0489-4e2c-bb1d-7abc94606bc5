{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qvoEkEXDtojn6YyT8ZQA/ow2v3+7NzWJnIPfnYYBNAs=", "__NEXT_PREVIEW_MODE_ID": "7d534acb99973ae7fabcc01de6bbdb64", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "04c4b370c6ca63470add010a7e3009923c0fcaaee9046e96778c081e8143efbd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8223a3ea2259a4f723dd243c2ea7b7691120afd76dbe74b5d575c9894102f086"}}}, "sortedMiddleware": ["/"], "functions": {}}