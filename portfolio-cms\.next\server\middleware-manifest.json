{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qvoEkEXDtojn6YyT8ZQA/ow2v3+7NzWJnIPfnYYBNAs=", "__NEXT_PREVIEW_MODE_ID": "4709c530dbef0a2cc6e047ec72536c54", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7bed200c3cec0efa0b55f7d0627d89e2cc729ea781e04ca7ead010f89b59d33d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "46d35518a137313668bca7d2679614db3766a70e669ef636440ff1837734fa39"}}}, "sortedMiddleware": ["/"], "functions": {}}