import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Reel from '@/models/Reel';
import { requireAuth } from '@/lib/auth';
import { withCors } from '@/lib/cors';
import slugify from 'slugify';

// GET /api/reels - Get all reels with pagination and filters
export const GET = withCors(async (request: NextRequest) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const platform = searchParams.get('platform');
    const featured = searchParams.get('featured');
    const search = searchParams.get('search');

    // Build query
    const query: any = {};

    if (status) query.status = status;
    if (platform) query.platform = platform;
    if (featured !== null) query.featured = featured === 'true';
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
      ];
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get reels with pagination
    const reels = await Reel.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count
    const total = await Reel.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: reels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get reels error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// POST /api/reels - Create new reel
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      id,
      title,
      description,
      platform,
      embedUrl,
      featured = false,
      status = 'draft',
      order = 0,
    } = data;

    // Validate required fields
    if (!id || !title || !platform) {
      return NextResponse.json(
        { error: 'ID, title, and platform are required' },
        { status: 400 }
      );
    }

    // Generate slug
    const baseSlug = slugify(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Ensure unique slug
    let slug = baseSlug;
    let counter = 1;
    while (await Reel.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Create reel
    const reel = new Reel({
      id,
      title,
      slug,
      description,
      platform,
      embedUrl,
      featured,
      status,
      order,
    });

    await reel.save();

    return NextResponse.json({
      success: true,
      data: reel,
    }, { status: 201 });
  } catch (error) {
    console.error('Create reel error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
