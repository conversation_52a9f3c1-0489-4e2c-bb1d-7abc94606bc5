import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Short from "@/models/Short";
import { requireAuth } from "@/lib/auth";
import { withCors } from "@/lib/cors";
import slugify from "slugify";

// GET /api/reels - Get all shorts with pagination and filters
export const GET = withCors(async (request: NextRequest) => {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const platform = searchParams.get("platform");
    const featured = searchParams.get("featured");
    const search = searchParams.get("search");

    // Build query
    const query: any = {};

    if (status) query.status = status;
    if (platform) query.platform = platform;
    if (featured !== null) query.featured = featured === "true";
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
        { tags: { $in: [new RegExp(search, "i")] } },
      ];
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get shorts with pagination
    const shorts = await Short.find(query)
      .sort({ order: 1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Add auto-generated thumbnails for YouTube shorts that don't have them
    const shortsWithThumbnails = shorts.map((short) => ({
      ...short,
      thumbnail:
        short.thumbnail ||
        (short.platform === "youtube" && short.id
          ? `https://img.youtube.com/vi/${short.id}/maxresdefault.jpg`
          : "/images/placeholder.svg"),
    }));

    // Get total count
    const total = await Short.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: shortsWithThumbnails,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get reels error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// POST /api/reels - Create new reel
export const POST = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const data = await request.json();
    const {
      id,
      title,
      description,
      platform,
      embedUrl,
      featured = false,
      status = "draft",
      order = 0,
    } = data;

    // Validate required fields
    if (!id || !title) {
      return NextResponse.json(
        { error: "ID and title are required" },
        { status: 400 }
      );
    }

    // Ensure platform is YouTube (only supported platform)
    const finalPlatform = "youtube";

    // Auto-generate thumbnail for YouTube
    const thumbnail = `https://img.youtube.com/vi/${id}/maxresdefault.jpg`;

    // Generate slug
    const baseSlug = slugify(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });

    // Ensure unique slug
    let slug = baseSlug;
    let counter = 1;
    while (await Short.findOne({ slug })) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Create short
    const short = new Short({
      id,
      title,
      slug,
      description,
      platform: finalPlatform,
      embedUrl,
      thumbnail,
      featured,
      status,
      order,
    });

    await short.save();

    return NextResponse.json(
      {
        success: true,
        data: short,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Create reel error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});
