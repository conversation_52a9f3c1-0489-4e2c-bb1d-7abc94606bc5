export interface Video {
  id: string;
  title: string;
  type: 'youtube' | 'vimeo';
  thumbnail?: string;
  description?: string;
  category?: string;
}

export const videos: Video[] = [
  {
    id: 'EPoaSIIOVQM',
    title: 'Corporate Brand Story',
    type: 'youtube',
    description: 'A compelling corporate brand story showcasing modern editing techniques and professional storytelling.',
    category: 'Corporate'
  },
  {
    id: 'nfWlot6h_JM',
    title: 'Travel Vlog Highlights',
    type: 'youtube',
    description: 'Dynamic travel vlog editing with smooth transitions and engaging pacing.',
    category: 'Travel'
  },
  {
    id: 'LXb3EKWsInQ',
    title: 'Product Demo Reel',
    type: 'youtube',
    description: 'Professional product demonstration with clean cuts and effective visual storytelling.',
    category: 'Commercial'
  },
  {
    id: 'rokGy0huYEA',
    title: 'Event Recap Montage',
    type: 'youtube',
    description: 'High-energy event recap capturing the excitement and atmosphere of live events.',
    category: 'Event'
  },
  {
    id: '6l7J1i1OD_s',
    title: 'Short Film Trailer',
    type: 'youtube',
    description: 'Cinematic trailer editing with dramatic pacing and emotional storytelling.',
    category: 'Film'
  },
  {
    id: 'z_AbfPXTKms',
    title: 'Music Video Edit',
    type: 'youtube',
    description: 'Creative music video editing with rhythm-based cuts and visual effects.',
    category: 'Music'
  }
];
