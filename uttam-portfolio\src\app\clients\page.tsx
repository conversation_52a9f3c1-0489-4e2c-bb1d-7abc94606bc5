import { Metadata } from 'next';
import { getClients, getFeaturedClients } from '@/lib/api';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import ClientsPageClient from './ClientsPageClient';

export const metadata: Metadata = {
  title: 'Our Clients - Trusted Partnerships',
  description: 'Discover the diverse range of clients we have worked with across various industries. Building lasting partnerships through exceptional video editing services.',
  openGraph: {
    title: 'Our Clients - Uttam Rimal',
    description: 'Discover the diverse range of clients we have worked with across various industries. Building lasting partnerships through exceptional video editing services.',
  },
};

export default async function ClientsPage() {
  // Fetch data on the server side
  const [allClients, featuredClients] = await Promise.all([
    getClients(),
    getFeaturedClients(),
  ]);

  return (
    <main>
      <Header />
      <ClientsPageClient allClients={allClients} featuredClients={featuredClients} />
      <Footer />
      <ScrollToTop />
    </main>
  );
}
