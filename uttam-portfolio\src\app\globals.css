@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Merriweather:wght@700&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-heading: 'Merriweather', serif;
  --font-body: 'Poppins', sans-serif;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Portfolio custom colors */
  --color-portfolio-primary: #1a2a6c;
  --color-portfolio-secondary: #f45c43;
  --color-portfolio-accent: #00bcd4;
  --color-portfolio-dark: #0f1d3a;
  --color-portfolio-light: #f8f9fa;
  --color-portfolio-text: #343a40;

  /* Custom animations */
  --animate-float: float 6s ease-in-out infinite alternate;
  --animate-bounce-slow: bounce 2s infinite;
}

:root {
  --radius: 0.625rem;
  --background: #f8f9fa;
  --foreground: #343a40;
  --card: #ffffff;
  --card-foreground: #343a40;
  --popover: #ffffff;
  --popover-foreground: #343a40;
  --primary: #1a2a6c;
  --primary-foreground: #ffffff;
  --secondary: #f45c43;
  --secondary-foreground: #ffffff;
  --muted: #f8f9fa;
  --muted-foreground: #6c757d;
  --accent: #00bcd4;
  --accent-foreground: #0f1d3a;
  --destructive: #dc3545;
  --border: #dee2e6;
  --input: #f8f9fa;
  --ring: #1a2a6c;
  --chart-1: #1a2a6c;
  --chart-2: #f45c43;
  --chart-3: #00bcd4;
  --chart-4: #0f1d3a;
  --chart-5: #6c757d;
  --sidebar: #ffffff;
  --sidebar-foreground: #343a40;
  --sidebar-primary: #1a2a6c;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f8f9fa;
  --sidebar-accent-foreground: #343a40;
  --sidebar-border: #dee2e6;
  --sidebar-ring: #1a2a6c;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-body;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading text-primary;
  }
}

@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite alternate;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .text-portfolio-primary {
    color: var(--color-portfolio-primary);
  }

  .text-portfolio-secondary {
    color: var(--color-portfolio-secondary);
  }

  .text-portfolio-accent {
    color: var(--color-portfolio-accent);
  }

  .bg-portfolio-primary {
    background-color: var(--color-portfolio-primary);
  }

  .bg-portfolio-secondary {
    background-color: var(--color-portfolio-secondary);
  }

  .bg-portfolio-accent {
    background-color: var(--color-portfolio-accent);
  }

  .bg-portfolio-dark {
    background-color: var(--color-portfolio-dark);
  }

  .bg-portfolio-light {
    background-color: var(--color-portfolio-light);
  }
}

@keyframes float {
  from {
    transform: translateY(-10px);
  }
  to {
    transform: translateY(10px);
  }
}

/* WhatsApp Button Animation Delay */
.animation-delay-1000 {
  animation-delay: 1s;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.heartbeat {
  animation: heartbeat 1.5s infinite;
}

.blink {
  animation: blink 1.5s infinite;
}
