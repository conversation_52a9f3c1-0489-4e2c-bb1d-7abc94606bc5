@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-heading: "Playfair Display", serif;
  --font-body: "Inter", sans-serif;
  --font-accent: "Space Grotesk", sans-serif;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Portfolio custom colors */
  --color-portfolio-base: #121212;
  --color-portfolio-light: #f5f5f5;
  --color-portfolio-accent: #c6a664;
  --color-portfolio-text: #121212;
  --color-portfolio-text-light: #f5f5f5;

  /* Custom animations */
  --animate-float: float 6s ease-in-out infinite alternate;
  --animate-bounce-slow: bounce 2s infinite;
}

:root {
  --radius: 0.625rem;
  --background: #f5f5f5;
  --foreground: #121212;
  --card: #ffffff;
  --card-foreground: #121212;
  --popover: #ffffff;
  --popover-foreground: #121212;
  --primary: #121212;
  --primary-foreground: #f5f5f5;
  --secondary: #c6a664;
  --secondary-foreground: #121212;
  --muted: #f5f5f5;
  --muted-foreground: #6c757d;
  --accent: #c6a664;
  --accent-foreground: #121212;
  --destructive: #dc3545;
  --border: #e0e0e0;
  --input: #f5f5f5;
  --ring: #c6a664;
  --chart-1: #121212;
  --chart-2: #c6a664;
  --chart-3: #f5f5f5;
  --chart-4: #6c757d;
  --chart-5: #e0e0e0;
  --sidebar: #ffffff;
  --sidebar-foreground: #121212;
  --sidebar-primary: #121212;
  --sidebar-primary-foreground: #f5f5f5;
  --sidebar-accent: #f5f5f5;
  --sidebar-accent-foreground: #121212;
  --sidebar-border: #e0e0e0;
  --sidebar-ring: #1a2a6c;
}

.dark {
  --background: #121212;
  --foreground: #f5f5f5;
  --card: #1e1e1e;
  --card-foreground: #f5f5f5;
  --popover: #1e1e1e;
  --popover-foreground: #f5f5f5;
  --primary: #f5f5f5;
  --primary-foreground: #121212;
  --secondary: #c6a664;
  --secondary-foreground: #121212;
  --muted: #1e1e1e;
  --muted-foreground: #a0a0a0;
  --accent: #c6a664;
  --accent-foreground: #121212;
  --destructive: #dc3545;
  --border: #2a2a2a;
  --input: #1e1e1e;
  --ring: #c6a664;
  --chart-1: #f5f5f5;
  --chart-2: #c6a664;
  --chart-3: #121212;
  --chart-4: #a0a0a0;
  --chart-5: #2a2a2a;
  --sidebar: #1e1e1e;
  --sidebar-foreground: #f5f5f5;
  --sidebar-primary: #f5f5f5;
  --sidebar-primary-foreground: #121212;
  --sidebar-accent: #2a2a2a;
  --sidebar-accent-foreground: #f5f5f5;
  --sidebar-border: #2a2a2a;
  --sidebar-ring: #c6a664;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-body;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading text-primary;
  }
}

@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite alternate;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .text-portfolio-primary {
    color: var(--color-portfolio-primary);
  }

  .text-portfolio-secondary {
    color: var(--color-portfolio-secondary);
  }

  .text-portfolio-accent {
    color: var(--color-portfolio-accent);
  }

  .bg-portfolio-primary {
    background-color: var(--color-portfolio-primary);
  }

  .bg-portfolio-secondary {
    background-color: var(--color-portfolio-secondary);
  }

  .bg-portfolio-accent {
    background-color: var(--color-portfolio-accent);
  }

  .bg-portfolio-dark {
    background-color: var(--color-portfolio-dark);
  }

  .bg-portfolio-light {
    background-color: var(--color-portfolio-light);
  }
}

@keyframes float {
  from {
    transform: translateY(-10px);
  }
  to {
    transform: translateY(10px);
  }
}

/* WhatsApp Button Animation Delay */
.animation-delay-1000 {
  animation-delay: 1s;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Fade in up animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

.heartbeat {
  animation: heartbeat 1.5s infinite;
}

.blink {
  animation: blink 1.5s infinite;
}
