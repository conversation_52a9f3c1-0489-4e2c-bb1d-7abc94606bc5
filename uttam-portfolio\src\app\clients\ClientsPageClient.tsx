'use client';

import { useState } from 'react';
import ClientsPageHeader from '@/components/ClientsPageHeader';
import ClientsFilter from '@/components/ClientsFilter';
import FeaturedClientsSection from '@/components/FeaturedClientsSection';
import ClientsGrid from '@/components/ClientsGrid';
import ClientsStats from '@/components/ClientsStats';
import { type Client } from '@/lib/api';

interface ClientsPageClientProps {
  allClients: Client[];
  featuredClients: Client[];
}

export default function ClientsPageClient({ allClients, featuredClients }: ClientsPageClientProps) {
  const [filteredClients, setFilteredClients] = useState<Client[]>(allClients);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [industryFilter, setIndustryFilter] = useState<string>('all');

  // Get unique industries
  const industries = Array.from(new Set(allClients.map(client => client.industry).filter(Boolean))) as string[];

  const handleIndustryFilter = (industry: string) => {
    setIndustryFilter(industry);
    if (industry === 'all') {
      setFilteredClients(allClients);
    } else {
      setFilteredClients(allClients.filter(client => client.industry === industry));
    }
  };

  return (
    <div className="min-h-screen bg-portfolio-light">
      {/* Header */}
      <ClientsPageHeader />

      <div className="container mx-auto px-4 py-16">
        {/* Featured Clients */}
        <FeaturedClientsSection
          featuredClients={featuredClients}
          allClients={allClients}
        />

        {/* Filters and View Controls */}
        <ClientsFilter
          industries={industries}
          selectedIndustry={industryFilter}
          onIndustryChange={handleIndustryFilter}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          totalClients={filteredClients.length}
        />

        {/* All Clients */}
        <section>
          <h2 className="text-3xl font-heading font-bold text-portfolio-primary mb-8">
            {industryFilter === 'all' ? 'All Clients' : `${industryFilter} Clients`}
            <span className="text-lg font-normal text-muted-foreground ml-2">
              ({filteredClients.length} clients)
            </span>
          </h2>

          <ClientsGrid
            clients={filteredClients}
            allClients={allClients}
            viewMode={viewMode}
            industryFilter={industryFilter}
          />
        </section>

        {/* Stats Section */}
        <ClientsStats className="mt-20" />
      </div>
    </div>
  );
}
