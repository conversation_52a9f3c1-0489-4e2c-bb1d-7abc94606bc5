'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/DashboardLayout';
import RichTextEditor from '@/components/RichTextEditor';
import ImageUpload from '@/components/ImageUpload';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Save, Eye, X } from 'lucide-react';
import Link from 'next/link';

export default function NewBlogPostPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    title: '',
    excerpt: '',
    content: '',
    category: '',
    tags: [] as string[],
    featured: false,
    status: 'draft' as 'draft' | 'published' | 'archived',
    thumbnail: '',
  });

  const [tagInput, setTagInput] = useState('');

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Auto-generate SEO fields based on title and excerpt
  const generateSEOFields = () => {
    const seoTitle = formData.title.length > 60
      ? formData.title.substring(0, 57) + '...'
      : formData.title;

    const seoDescription = formData.excerpt.length > 160
      ? formData.excerpt.substring(0, 157) + '...'
      : formData.excerpt;

    // Generate keywords from title and category
    const keywords = [
      ...formData.title.toLowerCase().split(' ').filter(word => word.length > 3),
      formData.category.toLowerCase(),
      ...formData.tags.map(tag => tag.toLowerCase())
    ].filter(Boolean).slice(0, 10); // Limit to 10 keywords

    return {
      seoTitle,
      seoDescription,
      seoKeywords: [...new Set(keywords)] // Remove duplicates
    };
  };

  const handleSubmit = async (status: 'draft' | 'published') => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Auto-generate SEO fields
      const seoFields = generateSEOFields();

      const response = await fetch('/api/blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          ...formData,
          ...seoFields,
          status,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(`Blog post ${status === 'published' ? 'published' : 'saved as draft'} successfully!`);
        setTimeout(() => {
          router.push('/dashboard/blog');
        }, 1500);
      } else {
        setError(data.error || 'Failed to save blog post');
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/blog">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Posts
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">New Blog Post</h1>
              <p className="text-gray-600">Create a new blog post</p>
            </div>
          </div>
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="xl:col-span-3 space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Post Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter post title..."
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="excerpt">Excerpt *</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    placeholder="Brief description of the post..."
                    rows={3}
                    required
                  />
                </div>

                <div>
                  <Label>Content *</Label>
                  <RichTextEditor
                    content={formData.content}
                    onChange={(content) => handleInputChange('content', content)}
                    placeholder="Start writing your blog post..."
                  />
                </div>
              </CardContent>
            </Card>

            {/* SEO Preview */}
            <Card>
              <CardHeader>
                <CardTitle>SEO Preview</CardTitle>
                <p className="text-sm text-gray-600">Auto-generated from your content</p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-gray-50 p-6 rounded-lg border">
                  <div className="space-y-3">
                    <div className="text-blue-600 text-lg font-medium line-clamp-1">
                      {formData.title || 'Your blog post title will appear here'}
                    </div>
                    <div className="text-green-700 text-sm">
                      yourwebsite.com/blog/{formData.title ? formData.title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '') : 'post-slug'}
                    </div>
                    <div className="text-gray-700 text-sm line-clamp-2">
                      {formData.excerpt || 'Your post excerpt will appear here as the meta description...'}
                    </div>
                  </div>
                </div>

                {(formData.title || formData.excerpt || formData.category || formData.tags.length > 0) && (
                  <div>
                    <Label className="text-sm font-medium">Auto-generated Keywords:</Label>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {generateSEOFields().seoKeywords.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6 xl:sticky xl:top-6 xl:h-fit">
            {/* Publish Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Publish</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => handleInputChange('featured', checked)}
                  />
                  <Label htmlFor="featured">Featured Post</Label>
                </div>

                <div className="space-y-3">
                  <Button
                    onClick={() => handleSubmit('draft')}
                    variant="outline"
                    className="w-full"
                    disabled={loading}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save Draft
                  </Button>

                  <Button
                    onClick={() => handleSubmit('published')}
                    className="w-full"
                    disabled={loading || !formData.title || !formData.excerpt || !formData.content}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Publish
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Post Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Post Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Input
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    placeholder="e.g., Tutorial, Tips, Review"
                    required
                  />
                </div>

                <div>
                  <Label>Tags</Label>
                  <div className="flex space-x-2">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add tag..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} variant="outline">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="flex items-center gap-1">
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <ImageUpload
                    label="Featured Image"
                    value={formData.thumbnail}
                    onChange={(url) => handleInputChange('thumbnail', url)}
                    folder="blog"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
