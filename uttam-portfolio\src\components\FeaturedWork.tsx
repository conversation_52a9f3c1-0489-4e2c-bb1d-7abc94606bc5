'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Play, X } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { getFeaturedVideos, getYouTubeThumbnail, type Video } from '@/lib/api';

export default function FeaturedWork() {
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const featuredVideos = await getFeaturedVideos();
        // Show only first 6 videos for featured section
        setVideos(featuredVideos.slice(0, 6));
      } catch (error) {
        console.error('Error fetching featured videos:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, []);

  const getYouTubeEmbedUrl = (videoId: string) => {
    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;
  };

  return (
    <section id="featured-work" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-heading font-bold text-portfolio-primary mb-4">
            Featured Work
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A selection of projects showcasing diverse editing styles and creative storytelling techniques.
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-card rounded-xl overflow-hidden shadow-lg">
                <div className="aspect-video bg-gray-200 animate-pulse"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-200 rounded animate-pulse mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : videos.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">No featured videos available at the moment.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {videos.map((video, index) => (
              <div
                key={video._id}
                className="group bg-card rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105"
              >
              <div className="relative aspect-video overflow-hidden">
                <Image
                  src={getYouTubeThumbnail(video.id)}
                  alt={video.title}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                />

                {/* Play Button Overlay */}
                <Dialog>
                  <DialogTrigger asChild>
                    <button
                      className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      onClick={() => setSelectedVideo(video.id)}
                    >
                      <div className="w-16 h-16 bg-portfolio-secondary hover:bg-portfolio-secondary/90 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg">
                        <Play size={24} className="text-white ml-1" fill="currentColor" />
                      </div>
                    </button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl w-full p-0 bg-black border-0">
                    <div className="relative aspect-video">
                      <button
                        onClick={() => setSelectedVideo(null)}
                        className="absolute -top-12 right-0 z-50 text-white hover:text-portfolio-accent transition-colors"
                      >
                        <X size={24} />
                      </button>
                      {selectedVideo === video.id && (
                        <iframe
                          src={getYouTubeEmbedUrl(video.id)}
                          title={video.title}
                          className="w-full h-full"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                        />
                      )}
                    </div>
                  </DialogContent>
                </Dialog>

                {/* Category Badge */}
                {video.category && (
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="bg-portfolio-accent/90 text-white">
                      {video.category}
                    </Badge>
                  </div>
                )}
              </div>

              <div className="p-6">
                <h3 className="text-xl font-heading font-semibold text-portfolio-primary mb-2 group-hover:text-portfolio-secondary transition-colors duration-300">
                  {video.title}
                </h3>
                {video.description && (
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {video.description}
                  </p>
                )}
              </div>
            </div>
            ))}
          </div>
        )}

        {/* View More Button */}
        <div className="text-center mt-12">
          <Link href="/videos">
            <button className="bg-portfolio-primary hover:bg-portfolio-primary/90 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
              View All Videos
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
