# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/portfolio-cms

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Next Auth Secret
NEXTAUTH_SECRET=your-nextauth-secret-key

# App URL
NEXTAUTH_URL=http://localhost:3002

# Admin Credentials (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Portfolio Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=groceease
CLOUDINARY_API_KEY=761964628892988
CLOUDINARY_API_SECRET=oGyuENSaOTOlz03RR9ckPBjWSqU
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=groceease
