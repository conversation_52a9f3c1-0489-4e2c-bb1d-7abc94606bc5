import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import BlogPost from "@/models/BlogPost";
import { requireAuth } from "@/lib/auth";
import slugify from "slugify";

// Utility to extract ID from the URL
function extractId(req: NextRequest) {
  const segments = req.nextUrl.pathname.split("/");
  return segments[segments.length - 1]; // last part should be the ID or slug
}

// GET /api/blog/[id]
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const id = extractId(request);

    const post = await BlogPost.findOne({
      $or: [{ _id: id }, { slug: id }],
    }).lean();

    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: post,
    });
  } catch (error) {
    console.error("Get blog post error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/blog/[id]
export const PUT = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const id = extractId(request);
    const data = await request.json();

    const post = await BlogPost.findById(id);
    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    if (data.title && data.title !== post.title) {
      const baseSlug = slugify(data.title, {
        lower: true,
        strict: true,
        remove: /[*+~.()'"!:@]/g,
      });

      let slug = baseSlug;
      let counter = 1;
      while (await BlogPost.findOne({ slug, _id: { $ne: id } })) {
        slug = `${baseSlug}-${counter++}`;
      }
      data.slug = slug;
    }

    if (data.status === "published" && post.status !== "published") {
      data.publishedAt = new Date();
    }

    if (data.content && data.content !== post.content) {
      data.readTime = Math.ceil(data.content.split(" ").length / 200);
    }

    const updatedPost = await BlogPost.findByIdAndUpdate(
      id,
      { ...data, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    return NextResponse.json({ success: true, data: updatedPost });
  } catch (error) {
    console.error("Update blog post error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});

// DELETE /api/blog/[id]
export const DELETE = requireAuth(async (request: NextRequest) => {
  try {
    await connectDB();

    const id = extractId(request);

    const post = await BlogPost.findById(id);
    if (!post) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    await BlogPost.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: "Blog post deleted successfully",
    });
  } catch (error) {
    console.error("Delete blog post error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
});
