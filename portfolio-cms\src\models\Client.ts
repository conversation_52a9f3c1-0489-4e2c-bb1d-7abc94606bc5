import mongoose, { Document, Schema } from 'mongoose';

export interface IClient extends Document {
  name: string;
  slug: string;
  logo: string;
  description: string;
  website?: string;
  industry?: string;
  projectType?: string;
  featured: boolean;
  status: 'draft' | 'published' | 'archived';
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const ClientSchema = new Schema<IClient>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  logo: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000,
  },
  website: {
    type: String,
    trim: true,
  },
  industry: {
    type: String,
    trim: true,
    maxlength: 100,
  },
  projectType: {
    type: String,
    trim: true,
    maxlength: 200,
  },
  featured: {
    type: Boolean,
    default: false,
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  },
  order: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Indexes
ClientSchema.index({ slug: 1 });
ClientSchema.index({ status: 1, order: 1 });
ClientSchema.index({ featured: 1 });
ClientSchema.index({ industry: 1 });

// Pre-save middleware to auto-generate slug
ClientSchema.pre('save', function(next) {
  if (!this.slug && this.name) {
    const slugify = require('slugify');
    this.slug = slugify(this.name, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });
  }
  next();
});

export default mongoose.models.Client || mongoose.model<IClient>('Client', ClientSchema);
