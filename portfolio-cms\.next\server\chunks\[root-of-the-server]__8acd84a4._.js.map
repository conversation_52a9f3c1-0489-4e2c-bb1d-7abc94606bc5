{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI!;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\ninterface MongooseCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongoose: MongooseCache | undefined;\n}\n\nlet cached: MongooseCache = global.mongoose || { conn: null, promise: null };\n\nif (!global.mongoose) {\n  global.mongoose = cached;\n}\n\nasync function connectDB(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      console.log('✅ Connected to MongoDB');\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAWA,IAAI,SAAwB,OAAO,QAAQ,IAAI;IAAE,MAAM;IAAM,SAAS;AAAK;AAE3E,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;AACpB;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/src/models/Short.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from \"mongoose\";\n\nexport interface IShort extends Document {\n  id: string;\n  title: string;\n  slug: string;\n  description?: string;\n  platform: \"youtube\";\n  embedUrl?: string;\n  thumbnail?: string;\n  featured: boolean;\n  status: \"draft\" | \"published\" | \"archived\";\n  order: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ShortSchema = new Schema<IShort>(\n  {\n    id: {\n      type: String,\n      required: true,\n      trim: true,\n    },\n    title: {\n      type: String,\n      required: true,\n      trim: true,\n      maxlength: 200,\n    },\n    slug: {\n      type: String,\n      required: true,\n      unique: true,\n      lowercase: true,\n      trim: true,\n    },\n    description: {\n      type: String,\n      maxlength: 500,\n    },\n    platform: {\n      type: String,\n      enum: [\"youtube\"],\n      required: true,\n      default: \"youtube\",\n    },\n    embedUrl: {\n      type: String,\n    },\n    thumbnail: {\n      type: String,\n    },\n    featured: {\n      type: Boolean,\n      default: false,\n    },\n    status: {\n      type: String,\n      enum: [\"draft\", \"published\", \"archived\"],\n      default: \"draft\",\n    },\n    order: {\n      type: Number,\n      default: 0,\n    },\n  },\n  {\n    timestamps: true,\n  }\n);\n\n// Indexes for better performance\nShortSchema.index({ slug: 1 });\nShortSchema.index({ status: 1, order: 1 });\nShortSchema.index({ platform: 1 });\nShortSchema.index({ featured: 1 });\n\n// Pre-save middleware to auto-generate slug and embedUrl\nShortSchema.pre(\"save\", function (next) {\n  if (!this.slug && this.title) {\n    const slugify = require(\"slugify\");\n    this.slug = slugify(this.title, {\n      lower: true,\n      strict: true,\n      remove: /[*+~.()'\"!:@]/g,\n    });\n  }\n\n  // Auto-generate embed URL and thumbnail for YouTube Shorts\n  if (this.id && this.platform === \"youtube\") {\n    if (!this.embedUrl) {\n      this.embedUrl = `https://www.youtube.com/embed/${this.id}`;\n    }\n    if (!this.thumbnail) {\n      this.thumbnail = `https://img.youtube.com/vi/${this.id}/maxresdefault.jpg`;\n    }\n  }\n\n  next();\n});\n\nexport default mongoose.models.Short ||\n  mongoose.model<IShort>(\"Short\", ShortSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAiBA,MAAM,cAAc,IAAI,yGAAA,CAAA,SAAM,CAC5B;IACE,IAAI;QACF,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,WAAW;IACb;IACA,UAAU;QACR,MAAM;QACN,MAAM;YAAC;SAAU;QACjB,UAAU;QACV,SAAS;IACX;IACA,UAAU;QACR,MAAM;IACR;IACA,WAAW;QACT,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,iCAAiC;AACjC,YAAY,KAAK,CAAC;IAAE,MAAM;AAAE;AAC5B,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,OAAO;AAAE;AACxC,YAAY,KAAK,CAAC;IAAE,UAAU;AAAE;AAChC,YAAY,KAAK,CAAC;IAAE,UAAU;AAAE;AAEhC,yDAAyD;AACzD,YAAY,GAAG,CAAC,QAAQ,SAAU,IAAI;IACpC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;QAC5B,MAAM;QACN,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,KAAK,EAAE;YAC9B,OAAO;YACP,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,2DAA2D;IAC3D,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,QAAQ,KAAK,WAAW;QAC1C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,EAAE,EAAE;QAC5D;QACA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC;QAC5E;IACF;IAEA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAClC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/src/lib/auth.ts"], "sourcesContent": ["import jwt from 'jsonwebtoken';\nimport bcrypt from 'bcryptjs';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET!;\n\nif (!JWT_SECRET) {\n  throw new Error('Please define the JWT_SECRET environment variable');\n}\n\nexport interface JWTPayload {\n  userId: string;\n  email: string;\n  role: string;\n  iat?: number;\n  exp?: number;\n}\n\nexport const hashPassword = async (password: string): Promise<string> => {\n  const saltRounds = 12;\n  return await bcrypt.hash(password, saltRounds);\n};\n\nexport const comparePassword = async (\n  password: string,\n  hashedPassword: string\n): Promise<boolean> => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n\nexport const generateToken = (payload: Omit<JWTPayload, 'iat' | 'exp'>): string => {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '7d', // Token expires in 7 days\n  });\n};\n\nexport const verifyToken = (token: string): JWTPayload | null => {\n  try {\n    return jwt.verify(token, JWT_SECRET) as JWTPayload;\n  } catch (error) {\n    return null;\n  }\n};\n\nexport const getTokenFromRequest = (request: NextRequest): string | null => {\n  // Check Authorization header\n  const authHeader = request.headers.get('authorization');\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7);\n  }\n\n  // Check cookies\n  const token = request.cookies.get('auth-token')?.value;\n  return token || null;\n};\n\nexport const getUserFromRequest = async (request: NextRequest): Promise<JWTPayload | null> => {\n  const token = getTokenFromRequest(request);\n  if (!token) {\n    return null;\n  }\n\n  return verifyToken(token);\n};\n\n// Middleware helper for protected routes\nexport const requireAuth = (handler: Function) => {\n  return async (request: NextRequest, context: any) => {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    // Add user to request context\n    (request as any).user = user;\n    return handler(request, context);\n  };\n};\n\n// Admin-only middleware\nexport const requireAdmin = (handler: Function) => {\n  return async (request: NextRequest, context: any) => {\n    const user = await getUserFromRequest(request);\n    \n    if (!user) {\n      return new Response(\n        JSON.stringify({ error: 'Authentication required' }),\n        {\n          status: 401,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    if (user.role !== 'admin') {\n      return new Response(\n        JSON.stringify({ error: 'Admin access required' }),\n        {\n          status: 403,\n          headers: { 'Content-Type': 'application/json' },\n        }\n      );\n    }\n\n    (request as any).user = user;\n    return handler(request, context);\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU;AAEzC,IAAI,CAAC,YAAY;IACf,MAAM,IAAI,MAAM;AAClB;AAUO,MAAM,eAAe,OAAO;IACjC,MAAM,aAAa;IACnB,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAEO,MAAM,kBAAkB,OAC7B,UACA;IAEA,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AACxC;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;IACb;AACF;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,6BAA6B;IAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,gBAAgB;IAChB,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,QAAQ,oBAAoB;IAClC,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,OAAO,YAAY;AACrB;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,OAAO,SAAsB;QAClC,MAAM,OAAO,MAAM,mBAAmB;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,8BAA8B;QAC7B,QAAgB,IAAI,GAAG;QACxB,OAAO,QAAQ,SAAS;IAC1B;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,OAAO,OAAO,SAAsB;QAClC,MAAM,OAAO,MAAM,mBAAmB;QAEtC,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA0B,IAClD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAAwB,IAChD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEC,QAAgB,IAAI,GAAG;QACxB,OAAO,QAAQ,SAAS;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/src/lib/cors.ts"], "sourcesContent": ["// CORS middleware for API routes\nimport { NextRequest, NextResponse } from 'next/server';\n\nexport function corsHeaders(origin?: string) {\n  const allowedOrigins = [\n    'http://localhost:3000',\n    'http://localhost:3001',\n    'http://localhost:3002',\n    'http://localhost:3003',\n    'https://your-portfolio-domain.com', // Add your production domain here\n  ];\n\n  const isAllowedOrigin = origin && allowedOrigins.includes(origin);\n\n  return {\n    'Access-Control-Allow-Origin': isAllowedOrigin ? origin : allowedOrigins[0],\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    'Access-Control-Allow-Credentials': 'true',\n  };\n}\n\nexport function handleCors(request: NextRequest) {\n  const origin = request.headers.get('origin');\n  const headers = corsHeaders(origin || undefined);\n\n  // Handle preflight requests\n  if (request.method === 'OPTIONS') {\n    return new NextResponse(null, { status: 200, headers });\n  }\n\n  return headers;\n}\n\nexport function withCors(handler: (request: NextRequest) => Promise<NextResponse>) {\n  return async (request: NextRequest) => {\n    const origin = request.headers.get('origin');\n    const corsHeadersObj = corsHeaders(origin || undefined);\n\n    // Handle preflight requests\n    if (request.method === 'OPTIONS') {\n      return new NextResponse(null, { status: 200, headers: corsHeadersObj });\n    }\n\n    try {\n      const response = await handler(request);\n\n      // Add CORS headers to the response\n      Object.entries(corsHeadersObj).forEach(([key, value]) => {\n        response.headers.set(key, value);\n      });\n\n      return response;\n    } catch (error) {\n      console.error('API Error:', error);\n      const errorResponse = NextResponse.json(\n        { success: false, message: 'Internal server error' },\n        { status: 500 }\n      );\n\n      // Add CORS headers to error response\n      Object.entries(corsHeadersObj).forEach(([key, value]) => {\n        errorResponse.headers.set(key, value);\n      });\n\n      return errorResponse;\n    }\n  };\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AACjC;;AAEO,SAAS,YAAY,MAAe;IACzC,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,kBAAkB,UAAU,eAAe,QAAQ,CAAC;IAE1D,OAAO;QACL,+BAA+B,kBAAkB,SAAS,cAAc,CAAC,EAAE;QAC3E,gCAAgC;QAChC,gCAAgC;QAChC,oCAAoC;IACtC;AACF;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,UAAU,YAAY,UAAU;IAEtC,4BAA4B;IAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;YAAE,QAAQ;YAAK;QAAQ;IACvD;IAEA,OAAO;AACT;AAEO,SAAS,SAAS,OAAwD;IAC/E,OAAO,OAAO;QACZ,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,iBAAiB,YAAY,UAAU;QAE7C,4BAA4B;QAC5B,IAAI,QAAQ,MAAM,KAAK,WAAW;YAChC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;gBAAE,QAAQ;gBAAK,SAAS;YAAe;QACvE;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ;YAE/B,mCAAmC;YACnC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAClD,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;YAC5B;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM,gBAAgB,gIAAA,CAAA,eAAY,CAAC,IAAI,CACrC;gBAAE,SAAS;gBAAO,SAAS;YAAwB,GACnD;gBAAE,QAAQ;YAAI;YAGhB,qCAAqC;YACrC,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAClD,cAAc,OAAO,CAAC,GAAG,CAAC,KAAK;YACjC;YAEA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Uttam-Portfolio/Uttam-Portfolio/portfolio-cms/src/app/api/reels/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport connectDB from \"@/lib/mongodb\";\nimport Short from \"@/models/Short\";\nimport { requireAuth } from \"@/lib/auth\";\nimport { withCors } from \"@/lib/cors\";\nimport slugify from \"slugify\";\n\n// GET /api/reels - Get all shorts with pagination and filters\nexport const GET = withCors(async (request: NextRequest) => {\n  try {\n    await connectDB();\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n    const status = searchParams.get(\"status\");\n    const platform = searchParams.get(\"platform\");\n    const featured = searchParams.get(\"featured\");\n    const search = searchParams.get(\"search\");\n\n    // Build query\n    const query: any = {};\n\n    if (status) query.status = status;\n    if (platform) query.platform = platform;\n    if (featured !== null) query.featured = featured === \"true\";\n    if (search) {\n      query.$or = [\n        { title: { $regex: search, $options: \"i\" } },\n        { description: { $regex: search, $options: \"i\" } },\n        { tags: { $in: [new RegExp(search, \"i\")] } },\n      ];\n    }\n\n    // Calculate skip\n    const skip = (page - 1) * limit;\n\n    // Get shorts with pagination\n    const shorts = await Short.find(query)\n      .sort({ order: 1, createdAt: -1 })\n      .skip(skip)\n      .limit(limit)\n      .lean();\n\n    // Add auto-generated thumbnails for YouTube shorts that don't have them\n    const shortsWithThumbnails = shorts.map((short) => ({\n      ...short,\n      thumbnail:\n        short.thumbnail ||\n        (short.platform === \"youtube\" && short.id\n          ? `https://img.youtube.com/vi/${short.id}/maxresdefault.jpg`\n          : \"/images/placeholder.svg\"),\n    }));\n\n    // Get total count\n    const total = await Short.countDocuments(query);\n\n    return NextResponse.json({\n      success: true,\n      data: reelsWithThumbnails,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit),\n      },\n    });\n  } catch (error) {\n    console.error(\"Get reels error:\", error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n});\n\n// POST /api/reels - Create new reel\nexport const POST = requireAuth(async (request: NextRequest) => {\n  try {\n    await connectDB();\n\n    const data = await request.json();\n    const {\n      id,\n      title,\n      description,\n      platform,\n      embedUrl,\n      featured = false,\n      status = \"draft\",\n      order = 0,\n    } = data;\n\n    // Validate required fields\n    if (!id || !title) {\n      return NextResponse.json(\n        { error: \"ID and title are required\" },\n        { status: 400 }\n      );\n    }\n\n    // Ensure platform is YouTube (only supported platform)\n    const finalPlatform = \"youtube\";\n\n    // Auto-generate thumbnail for YouTube\n    const thumbnail = `https://img.youtube.com/vi/${id}/maxresdefault.jpg`;\n\n    // Generate slug\n    const baseSlug = slugify(title, {\n      lower: true,\n      strict: true,\n      remove: /[*+~.()'\"!:@]/g,\n    });\n\n    // Ensure unique slug\n    let slug = baseSlug;\n    let counter = 1;\n    while (await Reel.findOne({ slug })) {\n      slug = `${baseSlug}-${counter}`;\n      counter++;\n    }\n\n    // Create reel\n    const reel = new Reel({\n      id,\n      title,\n      slug,\n      description,\n      platform: finalPlatform,\n      embedUrl,\n      thumbnail,\n      featured,\n      status,\n      order,\n    });\n\n    await reel.save();\n\n    return NextResponse.json(\n      {\n        success: true,\n        data: reel,\n      },\n      { status: 201 }\n    );\n  } catch (error) {\n    console.error(\"Create reel error:\", error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,MAAM,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;IACjC,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,cAAc;QACd,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ,MAAM,MAAM,GAAG;QAC3B,IAAI,UAAU,MAAM,QAAQ,GAAG;QAC/B,IAAI,aAAa,MAAM,MAAM,QAAQ,GAAG,aAAa;QACrD,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV;oBAAE,OAAO;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC3C;oBAAE,aAAa;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBACjD;oBAAE,MAAM;wBAAE,KAAK;4BAAC,IAAI,OAAO,QAAQ;yBAAK;oBAAC;gBAAE;aAC5C;QACH;QAEA,iBAAiB;QACjB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,6BAA6B;QAC7B,MAAM,SAAS,MAAM,wHAAA,CAAA,UAAK,CAAC,IAAI,CAAC,OAC7B,IAAI,CAAC;YAAE,OAAO;YAAG,WAAW,CAAC;QAAE,GAC/B,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;QAEP,wEAAwE;QACxE,MAAM,uBAAuB,OAAO,GAAG,CAAC,CAAC,QAAU,CAAC;gBAClD,GAAG,KAAK;gBACR,WACE,MAAM,SAAS,IACf,CAAC,MAAM,QAAQ,KAAK,aAAa,MAAM,EAAE,GACrC,CAAC,2BAA2B,EAAE,MAAM,EAAE,CAAC,kBAAkB,CAAC,GAC1D,yBAAyB;YACjC,CAAC;QAED,kBAAkB;QAClB,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,cAAc,CAAC;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IACrC,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,EAAE,EACF,KAAK,EACL,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,OAAO,EAChB,QAAQ,CAAC,EACV,GAAG;QAEJ,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,OAAO;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uDAAuD;QACvD,MAAM,gBAAgB;QAEtB,sCAAsC;QACtC,MAAM,YAAY,CAAC,2BAA2B,EAAE,GAAG,kBAAkB,CAAC;QAEtE,gBAAgB;QAChB,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;YAC9B,OAAO;YACP,QAAQ;YACR,QAAQ;QACV;QAEA,qBAAqB;QACrB,IAAI,OAAO;QACX,IAAI,UAAU;QACd,MAAO,MAAM,KAAK,OAAO,CAAC;YAAE;QAAK,GAAI;YACnC,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS;YAC/B;QACF;QAEA,cAAc;QACd,MAAM,OAAO,IAAI,KAAK;YACpB;YACA;YACA;YACA;YACA,UAAU;YACV;YACA;YACA;YACA;YACA;QACF;QAEA,MAAM,KAAK,IAAI;QAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}