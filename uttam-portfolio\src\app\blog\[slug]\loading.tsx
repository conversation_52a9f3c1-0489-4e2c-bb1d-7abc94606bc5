export default function Loading() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header Skeleton */}
      <div className="bg-portfolio-primary text-white py-12">
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-6 bg-white/20 rounded w-32 mb-8"></div>
            
            <div className="max-w-4xl">
              <div className="flex gap-2 mb-4">
                <div className="h-6 bg-white/20 rounded w-20"></div>
                <div className="h-6 bg-white/20 rounded w-16"></div>
              </div>
              
              <div className="h-12 bg-white/20 rounded w-3/4 mb-4"></div>
              
              <div className="flex gap-6 mb-6">
                <div className="h-4 bg-white/20 rounded w-24"></div>
                <div className="h-4 bg-white/20 rounded w-20"></div>
                <div className="h-4 bg-white/20 rounded w-28"></div>
              </div>
              
              <div className="h-6 bg-white/20 rounded w-full mb-2"></div>
              <div className="h-6 bg-white/20 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Image Skeleton */}
      <div className="relative aspect-video max-h-96 bg-gray-200 animate-pulse"></div>

      {/* Content Skeleton */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Main Content Skeleton */}
            <article className="flex-1 animate-pulse">
              <div className="space-y-4 mb-8">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              </div>
              
              <div className="space-y-4 mb-8">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>

              {/* Tags Skeleton */}
              <div className="mt-12 pt-8 border-t border-gray-200">
                <div className="h-6 bg-gray-200 rounded w-16 mb-4"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                  <div className="h-6 bg-gray-200 rounded w-20"></div>
                  <div className="h-6 bg-gray-200 rounded w-14"></div>
                </div>
              </div>

              {/* Share Skeleton */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="h-6 bg-gray-200 rounded w-32"></div>
                  <div className="h-10 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            </article>

            {/* Sidebar Skeleton */}
            <aside className="lg:w-80 animate-pulse">
              <div className="sticky top-8 space-y-8">
                {/* Author Info Skeleton */}
                <div className="bg-portfolio-light p-6 rounded-xl">
                  <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
                    <div>
                      <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-32"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>

                {/* Related Posts Skeleton */}
                <div className="bg-portfolio-light p-6 rounded-xl">
                  <div className="h-6 bg-gray-200 rounded w-28 mb-4"></div>
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i}>
                        <div className="h-4 bg-gray-200 rounded w-full mb-1"></div>
                        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </aside>
          </div>
        </div>
      </div>
    </div>
  );
}
