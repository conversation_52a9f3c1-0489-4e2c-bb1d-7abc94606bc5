'use client';

import { Filter, Grid, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ClientsFilterProps {
  industries: string[];
  selectedIndustry: string;
  onIndustryChange: (industry: string) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  totalClients: number;
}

export default function ClientsFilter({
  industries,
  selectedIndustry,
  onIndustryChange,
  viewMode,
  onViewModeChange,
  totalClients,
}: ClientsFilterProps) {
  return (
    <div className="bg-white rounded-xl shadow-sm border p-6 mb-8">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
        {/* Filter Section */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-portfolio-primary/10 rounded-lg">
              <Filter size={20} className="text-portfolio-primary" />
            </div>
            <span className="font-semibold text-portfolio-primary text-lg">
              Filter Clients
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-gray-600 whitespace-nowrap">
              Industry:
            </span>
            <Select value={selectedIndustry} onValueChange={onIndustryChange}>
              <SelectTrigger className="w-48 border-gray-200 focus:border-portfolio-primary">
                <SelectValue placeholder="Select industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                {industries.map((industry) => (
                  <SelectItem key={industry} value={industry}>
                    {industry}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results Count & View Mode */}
        <div className="flex items-center gap-6">
          <div className="text-sm text-gray-600">
            <span className="font-semibold text-portfolio-primary">{totalClients}</span>
            {' '}client{totalClients !== 1 ? 's' : ''} found
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-600 mr-2">View:</span>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="h-9 w-9 p-0"
            >
              <Grid size={16} />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="h-9 w-9 p-0"
            >
              <List size={16} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
