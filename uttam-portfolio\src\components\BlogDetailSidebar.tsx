import Link from 'next/link';
import { Calendar, Clock, ArrowRight, User, Award, BookOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatDate, type BlogPost } from '@/lib/api';

interface BlogDetailSidebarProps {
  relatedPosts: BlogPost[];
}

export default function BlogDetailSidebar({ relatedPosts }: BlogDetailSidebarProps) {
  return (
    <aside className="lg:w-80">
      <div className="sticky top-8 space-y-6">
        {/* Author Card */}
        <div className="bg-white rounded-xl shadow-md border p-6 hover:shadow-lg transition-shadow duration-300">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-portfolio-primary/10 rounded-lg">
              <User size={20} className="text-portfolio-primary" />
            </div>
            <h3 className="text-lg font-heading font-semibold text-portfolio-primary">
              About the Author
            </h3>
          </div>
          
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-portfolio-primary to-portfolio-secondary rounded-full flex items-center justify-center text-white font-heading font-bold text-xl shadow-lg">
              U
            </div>
            <div>
              <div className="font-semibold text-portfolio-primary text-lg">Uttam Rimal</div>
              <div className="text-sm text-muted-foreground">Video Editor & Storyteller</div>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
            Professional video editor with years of experience crafting visually stunning videos that engage and inspire audiences worldwide.
          </p>
          
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
            <div className="flex items-center gap-1">
              <Award size={12} />
              <span>5+ Years Experience</span>
            </div>
            <div className="flex items-center gap-1">
              <BookOpen size={12} />
              <span>50+ Articles</span>
            </div>
          </div>
          
          <Button asChild className="w-full bg-portfolio-primary hover:bg-portfolio-primary/90">
            <Link href="/#contact">
              Connect with Uttam
            </Link>
          </Button>
        </div>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <div className="bg-white rounded-xl shadow-md border p-6 hover:shadow-lg transition-shadow duration-300">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-portfolio-secondary/10 rounded-lg">
                <BookOpen size={20} className="text-portfolio-secondary" />
              </div>
              <h3 className="text-lg font-heading font-semibold text-portfolio-primary">
                Related Articles
              </h3>
            </div>
            
            <div className="space-y-4">
              {relatedPosts.map((relatedPost, index) => (
                <Link
                  key={relatedPost._id}
                  href={`/blog/${relatedPost.slug}`}
                  className="block group p-4 rounded-lg hover:bg-portfolio-light transition-all duration-300 border border-transparent hover:border-portfolio-primary/20"
                  style={{
                    animationDelay: `${index * 100}ms`,
                  }}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-portfolio-primary group-hover:text-portfolio-secondary transition-colors text-sm mb-2 line-clamp-2">
                        {relatedPost.title}
                      </h4>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar size={10} />
                          <span>{formatDate(relatedPost.createdAt)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={10} />
                          <span>{relatedPost.readTime} min</span>
                        </div>
                      </div>
                    </div>
                    <ArrowRight 
                      size={16} 
                      className="text-muted-foreground group-hover:text-portfolio-primary group-hover:translate-x-1 transition-all duration-300 flex-shrink-0" 
                    />
                  </div>
                </Link>
              ))}
            </div>
            
            <div className="mt-6 pt-4 border-t border-gray-100">
              <Button asChild variant="outline" className="w-full">
                <Link href="/blog">
                  View All Articles
                </Link>
              </Button>
            </div>
          </div>
        )}

        {/* Newsletter Signup */}
        <div className="bg-gradient-to-br from-portfolio-primary to-portfolio-secondary text-white rounded-xl p-6 shadow-lg">
          <div className="text-center">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📧</span>
            </div>
            <h3 className="text-lg font-heading font-semibold mb-2">
              Stay Updated
            </h3>
            <p className="text-white/90 text-sm mb-4">
              Get the latest video editing tips and insights delivered to your inbox.
            </p>
            <Button 
              asChild
              variant="secondary"
              className="w-full bg-white text-portfolio-primary hover:bg-white/90"
            >
              <Link href="/#contact">
                Subscribe Now
              </Link>
            </Button>
          </div>
        </div>

        {/* Quick Links */}
        <div className="bg-white rounded-xl shadow-md border p-6">
          <h3 className="text-lg font-heading font-semibold text-portfolio-primary mb-4">
            Quick Links
          </h3>
          <div className="space-y-3">
            <Link 
              href="/videos" 
              className="flex items-center gap-3 text-sm text-muted-foreground hover:text-portfolio-primary transition-colors group"
            >
              <span className="text-lg">🎬</span>
              <span className="group-hover:translate-x-1 transition-transform duration-300">Video Portfolio</span>
            </Link>
            <Link 
              href="/reels" 
              className="flex items-center gap-3 text-sm text-muted-foreground hover:text-portfolio-primary transition-colors group"
            >
              <span className="text-lg">📱</span>
              <span className="group-hover:translate-x-1 transition-transform duration-300">Social Media Reels</span>
            </Link>
            <Link 
              href="/clients" 
              className="flex items-center gap-3 text-sm text-muted-foreground hover:text-portfolio-primary transition-colors group"
            >
              <span className="text-lg">🤝</span>
              <span className="group-hover:translate-x-1 transition-transform duration-300">Client Success Stories</span>
            </Link>
          </div>
        </div>
      </div>
    </aside>
  );
}
