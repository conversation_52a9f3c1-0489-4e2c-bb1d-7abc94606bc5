import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import BlogPost from "@/models/BlogPost";

// Public API endpoint for the portfolio frontend to fetch published blog posts
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const featured = searchParams.get("featured");
    const category = searchParams.get("category");

    // Build query - only published posts
    const query: any = { status: "published" };

    if (featured !== null) query.featured = featured === "true";
    if (category) query.category = category;

    // Calculate skip
    const skip = (page - 1) * limit;

    // Get published posts with pagination
    const posts = await BlogPost.find(query)
      .select("-content") // Exclude full content for list view
      .sort({ publishedAt: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count
    const total = await BlogPost.countDocuments(query);

    // Add CORS headers for frontend access
    const response = NextResponse.json({
      success: true,
      data: posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

    // CORS headers
    response.headers.set(
      "Access-Control-Allow-Origin",
      process.env.FRONTEND_URL || "*"
    );
    response.headers.set("Access-Control-Allow-Methods", "GET");
    response.headers.set("Access-Control-Allow-Headers", "Content-Type");

    return response;
  } catch (error) {
    console.error("Get public blog posts error:", error);

    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );

    // CORS headers even for errors
    response.headers.set(
      "Access-Control-Allow-Origin",
      process.env.FRONTEND_URL || "*"
    );

    return response;
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 });

  response.headers.set(
    "Access-Control-Allow-Origin",
    process.env.FRONTEND_URL || "*"
  );
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type");

  return response;
}
