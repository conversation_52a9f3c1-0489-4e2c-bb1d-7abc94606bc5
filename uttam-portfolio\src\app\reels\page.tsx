import { Metadata } from 'next';
import { getReels, getFeaturedReels } from '@/lib/api';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import ReelsPageClient from './ReelsPageClient';

export const metadata: Metadata = {
  title: 'Reels - Social Media Content',
  description: 'Watch all my social media reels, short-form content, and viral videos. Creative reels portfolio by Uttam Rimal.',
  openGraph: {
    title: 'Reels - Uttam Rimal',
    description: 'Watch all my social media reels, short-form content, and viral videos. Creative reels portfolio by Uttam Rimal.',
  },
};

export default async function ReelsPage() {
  // Fetch data on the server side
  const [allReels, featuredReels] = await Promise.all([
    getReels(),
    getFeaturedReels(),
  ]);

  return (
    <main>
      <Header />
      <ReelsPageClient allReels={allReels} featuredReels={featuredReels} />
      <Footer />
      <ScrollToTop />
    </main>
  );
}
