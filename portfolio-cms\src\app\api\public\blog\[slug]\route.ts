import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import BlogPost from "@/models/BlogPost";

// Public API endpoint for the portfolio frontend to fetch a single published blog post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    await connectDB();

    const { slug } = await params;

    // Find published post by slug
    const post = await BlogPost.findOne({
      slug,
      status: "published",
    }).lean();

    if (!post) {
      const response = NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );

      // CORS headers
      response.headers.set(
        "Access-Control-Allow-Origin",
        process.env.FRONTEND_URL || "*"
      );

      return response;
    }

    // Increment view count
    await BlogPost.findByIdAndUpdate(post._id, { $inc: { views: 1 } });

    // Add CORS headers for frontend access
    const response = NextResponse.json({
      success: true,
      data: {
        ...post,
        views: post.views + 1, // Return updated view count
      },
    });

    // CORS headers
    response.headers.set(
      "Access-Control-Allow-Origin",
      process.env.FRONTEND_URL || "*"
    );
    response.headers.set("Access-Control-Allow-Methods", "GET");
    response.headers.set("Access-Control-Allow-Headers", "Content-Type");

    return response;
  } catch (error) {
    console.error("Get public blog post error:", error);

    const response = NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );

    // CORS headers even for errors
    response.headers.set(
      "Access-Control-Allow-Origin",
      process.env.FRONTEND_URL || "*"
    );

    return response;
  }
}

// Handle preflight requests
export async function OPTIONS() {
  const response = new NextResponse(null, { status: 200 });

  response.headers.set(
    "Access-Control-Allow-Origin",
    process.env.FRONTEND_URL || "*"
  );
  response.headers.set("Access-Control-Allow-Methods", "GET, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type");

  return response;
}
